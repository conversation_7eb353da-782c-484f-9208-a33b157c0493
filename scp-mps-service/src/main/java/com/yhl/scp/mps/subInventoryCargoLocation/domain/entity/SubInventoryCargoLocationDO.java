package com.yhl.scp.mps.subInventoryCargoLocation.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;


@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class SubInventoryCargoLocationDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = -54773210557324909L;

        /**
     * 主键ID
     */
        private String id;
        /**
     * 公司
     */
        private String corporationCode;
        /**
     * 库存点代码
     */
        private String factoryCode;
        /**
     * 库存点名称
     */
        private String factoryName;
        /**
     * 仓库
     */
        private String stashCode;
        /**
     * 仓库描述
     */
        private String stashName;
        /**
     * 货位
     */
        private String freightSpaceCode;
        /**
     * 货位描述
     */
        private String freightSpaceName;
        /**
     * 来源
     */
        private String source;
        /**
     * 是否有效
     */
        private String valid;
        /**
     * ERP货位
     */
        private String erpFreightSpaceCode;
        /**
     * 更新时间
     */
        private Date updateTime;
        /**
     * 版本号
     */
        private Integer versionValue;

}
